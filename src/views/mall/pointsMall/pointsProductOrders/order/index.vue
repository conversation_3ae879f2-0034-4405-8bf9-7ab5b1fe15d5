<script lang="ts" setup>
import PointProductOrderModal from './PointProductOrderModal.vue'
import PointProductDetailModal  from '@/views/mall/pointsMall/pointsProductOrders/order/PointProductDetailModal.vue';
import DeliveryModal from './DeliveryModal.vue'
import { columns, searchFormSchema } from './pointProductOrder.data'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useModal } from '@/components/Modal'
import { IconEnum } from '@/enums/appEnum'
import { BasicTable, TableAction, useTable } from '@/components/Table'
import { exportPointProductOrder, getPointProductOrderPage, updatePointProductOrder } from '@/api/mall/pointsMall/pointsProductOrders/order'
import { TradeOrderStatusEnum } from '@/enums/systemEnum'

defineOptions({ name: 'PointProductOrder' })

const { t } = useI18n()
const { createConfirm, createMessage } = useMessage()
const [registerModal, { openModal }] = useModal()
// 为详情弹窗注册一个新的 Modal 控制器
const [registerDetailModal, { openModal: openDetailModal }] = useModal()
// 为发货弹窗注册一个新的 Modal 控制器
const [registerDeliveryModal, { openModal: openDeliveryModal }] = useModal()
const [registerTable, { getForm, reload }] = useTable({
  title: '积分商城订单列表',
  api: getPointProductOrderPage,
  columns,
  formConfig: { labelWidth: 120, schemas: searchFormSchema },
  useSearchForm: true,
  showIndexColumn: false,
  rowKey: 'id', // 指定行的唯一键
  showTableSetting: true,
  actionColumn: {
    width: 200,
    title: t('common.action'),
    dataIndex: 'action',
    fixed: 'right',
  },
})

function handleCreate() {
  openModal(true, { isUpdate: false })
}

function handleEdit(record: Recordable) {
  openModal(true, { record, isUpdate: true })
}

function handleDetail(record: Recordable) {
  // 使用新的 openDetailModal 打开详情弹窗
  openDetailModal(true, { record, isDetail: true })
}

// 处理发货
function handleDelivery(record: Recordable) {
  openDeliveryModal(true, { record })
}

// 处理取消订单
async function handleCancel(record: Recordable) {
  createConfirm({
    title: '确认取消订单',
    iconType: 'warning',
    content: '确认要取消该订单吗？',
    async onOk() {
      try {
        // 更新订单状态为已取消
        await updatePointProductOrder({
          ...record,
          orderStatus: TradeOrderStatusEnum.CANCELED.status, // 40 - 已取消
        })
        createMessage.success('订单已取消')
        reload()
      } catch (error) {
        createMessage.error('取消订单失败')
      }
    },
  })
}

async function handleExport() {
  createConfirm({
    title: t('common.exportTitle'),
    iconType: 'warning',
    content: t('common.exportMessage'),
    async onOk() {
      await exportPointProductOrder(getForm().getFieldsValue())
      createMessage.success(t('common.exportSuccessText'))
    },
  })
}

// 获取表格操作按钮
function getTableActions(record: Recordable) {
  const actions = [
    {
      icon: IconEnum.VIEW,
      label: t('action.detail'),
      onClick: handleDetail.bind(null, record),
    },
    {
      icon: IconEnum.EDIT,
      label: t('action.edit'),
      auth: 'pointMall:point-product-order:update',
      onClick: handleEdit.bind(null, record),
    },
  ]

  // 只有待发货状态（orderStatus === 10）才显示发货和取消按钮
  if (record.orderStatus === 10) {
    actions.push(
      {
        icon: IconEnum.EDIT,
        label: '发货',
        onClick: handleDelivery.bind(null, record),
      },
      {
        icon: IconEnum.EDIT,
        label: '取消',
        danger: true,
        popConfirm: {
          title: '确认要取消该订单吗？',
          placement: 'left',
          confirm: handleCancel.bind(null, record),
        },
      }
    )
  }

  return actions
}

</script>
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="['pointMall:point-product-order:create']" :preIcon="IconEnum.ADD" @click="handleCreate">
          {{ t('action.create') }}
        </a-button>
        <a-button v-auth="['pointMall:point-product-order:export']" :preIcon="IconEnum.EXPORT" @click="handleExport">
          {{ t('action.export') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="getTableActions(record)"
          />
        </template>
      </template>
    </BasicTable>
    <PointProductOrderModal @register="registerModal" @success="reload()" />
    <!-- 添加新的详情弹窗组件 -->
    <PointProductDetailModal @register="registerDetailModal" />
    <!-- 添加发货弹窗组件 -->
    <DeliveryModal @register="registerDeliveryModal" @success="reload()" />
  </div>
</template>
