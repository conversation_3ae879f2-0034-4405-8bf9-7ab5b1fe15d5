import type { DescItem } from '@/components/Description'
import type { BasicColumn } from '@/components/Table'
import { TableImg, useRender } from '@/components/Table'
import { DICT_TYPE } from '@/utils/dict'
import { h } from 'vue'

// 订单详情信息配置
export const orderDetailSchema: DescItem[] = [
  // 第一行：订单状态、订单金额、积分金额、支付金额
  {
    label: '订单状态',
    field: 'orderStatus',
    render: val => useRender.renderDict(val, DICT_TYPE.TRADE_ORDER_STATUS),
  },
  {
    label: '订单金额',
    field: 'totalAmount',
    render: val => h('span', null, [
      h('span', { style: 'color: #FF0000; font-weight: bold' }, val ?? '0'),
      ' 元',
    ]),
  },
  {
    label: '积分金额',
    field: 'orderPoints',
    render: val => h('span', null, [
      h('span', { style: 'color: #FF0000; font-weight: bold' }, val ?? '0'),
      ' 积分',
    ]),
  },
  {
    label: '支付金额',
    field: 'payAmount',
    render: val => h('span', null, [
      h('span', { style: 'color: #FF0000; font-weight: bold' }, val ?? '0'),
      ' 元',
    ]),
  },

  // 第二行：下单时间、邮费、用户昵称、CLUBID
  {
    label: '下单时间',
    field: 'orderTime',
    render: val => useRender.renderDate(val),
  },
  {
    label: '邮费',
    field: 'postage',
    render: val => h('span', null, [
      h('span', { style: 'color: #FF0000; font-weight: bold' }, val ?? '0'),
      ' 元',
    ]),
  },
  { label: '用户昵称', field: 'nickname' },
  { label: 'CLUBID', field: 'memberNo' },

  // 第三行：收件人、联系电话、详细地址（占两列）
  { label: '收件人', field: 'consignee' },
  { label: '联系电话', field: 'phone' },
  {
    label: '详细地址',
    field: 'address',
    span: 2, // 占用两列
    render: (_val, record) => {
      const region = [record.province, record.city, record.district].filter(Boolean).join('')
      return region + (record.address || '')
    },
  },
  // 第四行：发货时间、物流公司、物流单号
  {
    label: '快递编码',
    field: 'logisticsEnname',

  },
  {
    label: '快递公司',
    field: 'logisticsName',
  },
  {
    label: '运单号',
    field: 'logisticsNum',
  },
  {
    label: '发货时间',
    field: 'deliveryTime',
    render: val => val ? useRender.renderDate(val) : '——',
  },
  // 第五行：是否虚拟、邮费类型、完成时间
  {
    label: '是否虚拟',
    field: 'isVirtual',
    render: val => useRender.renderDict(val, DICT_TYPE.POINT_VIRTUAL),
  },
  {
    label: '邮费类型',
    field: 'logisticsType',
    render: val => useRender.renderDict(val, DICT_TYPE.POINT_POST),
  },
  {
    label: '完成时间',
    field: 'completeTime',
    render: val => val ? useRender.renderDate(val) : '——',
  },
]

// 商品信息的数据结构（从订单明细接口获取）
export interface ProductDetailInfo {
  id: number // 订单明细ID
  orderId: number // 订单ID
  productId: number // 商品ID
  productName: string // 商品名称
  productLogo: string // 商品封面图
  skuId: number // 规格ID
  skuName: string // 规格名称
  skuLogo: string // 规格封面图
  skuIntegral: number // 积分值（单价）
  quantity: number // 数量
  totalIntegral: number // 总积分
  createTime: string // 创建时间
}

// 商品信息表格列配置
export const productColumns: BasicColumn[] = [
  {
    title: '规格封面',
    dataIndex: 'skuLogo',
    width: 100,
    customRender: ({ text }) => text
      ? h(TableImg, {
        imgList: [text],
        size: 60,
        simpleShow: true,
        showBadge: false,
        specifyStyle: {
          maxWidth: '60px',
          maxHeight: '60px',
          width: '60px',
          height: '60px',
        },
      })
      : h('span', { style: 'color: #999; font-size: 12px;' }, '暂无图片'),
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 200,
    customRender: ({ text }) => text || '——',
  },
  {
    title: '规格名称',
    dataIndex: 'skuName',
    width: 150,
    customRender: ({ text }) => text || '——',
  },
  {
    title: '积分单价',
    dataIndex: 'skuIntegral',
    width: 120,
    customRender: ({ text }) => h('span', { style: 'color: #FF0000; font-weight: bold;' }, `${text || 0} 积分`),
  },
  {
    title: '购买数量',
    dataIndex: 'quantity',
    width: 100,
  },
  {
    title: '总积分',
    dataIndex: 'totalIntegral',
    width: 120,
    customRender: ({ text }) => h('span', { style: 'color: #FF0000; font-weight: bold;' }, `${text || 0} 积分`),
  },
]
