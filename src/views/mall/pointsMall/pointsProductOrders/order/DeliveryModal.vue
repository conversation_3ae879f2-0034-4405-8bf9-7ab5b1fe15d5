<template>
  <BasicModal v-bind="$attrs" @register="registerModal" title="订单发货" @ok="handleSubmit" width="600px">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref, unref } from 'vue'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form/index'
import { updatePointProductOrder } from '@/api/mall/pointsMall/pointsProductOrders/order'
import { TradeOrderStatusEnum } from '@/enums/systemEnum'
import { useMessage } from '@/hooks/web/useMessage'
import { getSimpleDeliveryExpressList } from '@/api/mall/trade/delivery/express'
import { getPointSubOrderByOrderId } from '@/api/mall/pointsMall/pointsProductOrders/detail'
import { deliveryFormSchema } from './pointProductOrder.data'

defineOptions({ name: 'DeliveryModal' })

const emit = defineEmits(['success', 'register'])
const { createMessage } = useMessage()

const rowId = ref('')
const orderRecord = ref({})

const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
  labelWidth: 100,
  baseColProps: { span: 24 },
  schemas: deliveryFormSchema,
  showActionButtonGroup: false,
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  resetFields()
  setModalProps({ confirmLoading: false })

  if (data?.record) {
    rowId.value = data.record.id
    orderRecord.value = data.record

    // 获取快递公司列表
    try {
      const expressList = await getSimpleDeliveryExpressList()
      const expressOptions = expressList.map(item => ({
        label: item.name,
        value: item.id,
      }))

      // 更新快递公司选项
      updateSchema({
        field: 'logisticsId',
        componentProps: {
          options: expressOptions,
        },
      })
    } catch (error) {
      console.error('获取快递公司列表失败:', error)
    }

    // 获取订单商品信息
    let productInfo = '商品规格名称'
    try {
      const subOrders = await getPointSubOrderByOrderId(data.record.id)
      if (subOrders && subOrders.length > 0) {
        // 如果有多个商品，显示第一个商品的信息，或者可以显示商品数量
        const firstProduct = subOrders[0]
        productInfo = firstProduct.productName + (firstProduct.skuName ? ` - ${firstProduct.skuName}` : '')
        if (subOrders.length > 1) {
          productInfo += ` 等${subOrders.length}件商品`
        }
      }
    } catch (error) {
      console.error('获取订单商品信息失败:', error)
    }

    // 组装收货地址
    const fullAddress = `${data.record.province || ''}${data.record.city || ''}${data.record.district || ''}${data.record.address || ''}`

    setFieldsValue({
      orderNo: data.record.orderNo,
      productInfo: productInfo,
      orderQuantity: data.record.orderQuantity,
      orderPoints: data.record.orderPoints,
      postage: data.record.postage || 0,
      totalAmount: data.record.totalAmount,
      receiverName: data.record.receiverName,
      receiverMobile: data.record.receiverMobile,
      remark: data.record.remark || '请输入内容',
      receiverAddress: fullAddress,
      logisticsId: undefined,
      logisticsNum: '',
    })
  }
})

async function handleSubmit() {
  try {
    const values = await validate()
    setModalProps({ confirmLoading: true })
    
    // 更新订单状态为已发货，并添加物流信息
    await updatePointProductOrder({
      ...orderRecord.value,
      orderStatus: TradeOrderStatusEnum.DELIVERED.status, // 20 - 已发货
      deliveryTime: new Date().toISOString().slice(0, 19).replace('T', ' '), // 当前时间
      logisticsId: values.logisticsId,
      logisticsNum: values.logisticsNum,
    })
    
    createMessage.success('发货成功')
    closeModal()
    emit('success')
  } catch (error) {
    createMessage.error('发货失败')
  } finally {
    setModalProps({ confirmLoading: false })
  }
}
</script>
